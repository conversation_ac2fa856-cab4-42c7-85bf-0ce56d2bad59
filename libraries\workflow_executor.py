"""
此模块提供了 `WorkflowExecutor` 类，它是 MeowAgent 工作流执行逻辑的核心。
它负责解释和执行在 YAML 文件中定义或通过 Python 生成器实现的逐步工作流。
该执行器与 `WorkflowState` 紧密协作来跟踪工作流的当前状态（例如，当前步骤、步骤计数器、寄存器值等），
并与 `Agent` 实例交互以访问工具、库和其他代理级别功能。

主要职责和功能：

1.  **工作流生命周期管理**：
    *   处理用户消息的预处理 (`pre_process`)：在将控制权交给大型语言模型 (LLM) 之前，
        根据当前工作流状态和用户输入（如果提供）来推进工作流。
        对于 Python 工作流，这可能涉及向生成器发送用户输入并获取下一步。
        对于 YAML 工作流，它处理用户输入步骤 (`UserInputStep`) 的前进。
    *   处理 LLM 响应的后处理 (`post_process`)：在 LLM 生成响应（可能包括工具调用）后，
        验证该响应是否符合当前工作流步骤的要求。
        如果验证失败，它会生成指导性错误消息以提示 LLM 进行修正。
        如果验证成功，它会执行任何必要的后处理（例如，更新 `ExecuteStep` 的动作计数器），
        然后尝试自动前进到工作流的下一步。

2.  **步骤验证 (`_validate_step`及其私有子方法)**：
    *   针对不同类型的工作流步骤（`ExecuteStep`, `GenerateStep`, `ConditionStep`,
        `JumpStep`, `NopStep`, `HaltStep`, `UserInputStep`）提供细致的验证逻辑。
    *   `ExecuteStep` 验证：检查工具调用是否符合步骤中定义的动作组 (actions)，
        包括允许的工具名称、库引用以及每个组的最小/最大调用次数 (`min_calls`/`max_calls`)。
        同时验证 `workflow.next_step` 工具的正确使用时机。
    *   `GenerateStep` 验证：确保 LLM 生成文本内容并且不调用任何工具。
    *   `ConditionStep` 和条件 `JumpStep` 验证：期望 LLM 调用 `workflow.condition_branch` 工具来指示条件评估结果。
    *   无条件 `JumpStep` 和 `NopStep` 验证：期望 LLM 调用 `workflow.next_step` 工具。
    *   `HaltStep` 验证：期望 LLM 生成最终的文本回复，不调用工具。
    *   `UserInputStep` 验证：期望 LLM 不调用工具，并且其生成的提示用户输入的文本不应过长。
        此步骤的核心是等待实际的用户输入，而不是 LLM 的进一步动作。

3.  **自动步骤前进 (`_advance_step_if_needed`)**：
    *   在 LLM 的响应成功通过当前步骤的验证后，确定是否应自动前进到工作流的下一步。
    *   通常，如果 LLM 没有显式调用工作流控制工具（如 `next_step` 或 `condition_branch`），
        并且当前步骤的条件允许（例如，`ExecuteStep` 的所有 `max_calls` 已满足，或当前是 `GenerateStep`），
        则会自动前进。
    *   `UserInputStep` 是一个例外，它从不自动前进，总是等待外部用户输入。
    *   对于 Python 工作流，如果一个非交互步骤（非 `UserInputStep`）完成后，执行器会尝试
        连续自动执行后续的非交互步骤，直到遇到下一个 `UserInputStep`、工作流等待用户输入的状态被明确设置，
        或工作流结束。

4.  **Python 工作流交互**：
    *   通过 `WorkflowState` 中的方法（如 `advance_python_step_send` 和 `advance_python_step_next`）
        与 Python 工作流生成器进行交互。
    *   能够向生成器发送用户输入（当处于 `UserInputStep` 时），并从生成器获取后续步骤。

5.  **错误处理与指导**：
    *   当 LLM 的输出未能通过步骤验证时，会构造具体的错误消息，旨在指导 LLM
        如何修正其行为以符合工作流的要求。

`WorkflowExecutor` 将工作流的运行时控制逻辑从 `WorkflowLibrary` 中分离出来，
使得 `WorkflowLibrary` 可以更专注于接口定义、状态管理和与 Agent 的集成。
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable
import traceback

from message import AssistantMessage, UserMessage, Message, SystemMessage
from libraries.workflow_state import WorkflowState
from libraries.workflow_models import (
    WorkflowDefinition, WorkflowStep, ActionDefinition,
    ExecuteStep, ConditionStep, JumpStep, GenerateStep, NopStep, HaltStep, WorkflowOperation, UserInputStep, SwitchStep
)
from log import logger

class WorkflowExecutor:
    """
    封装工作流执行逻辑，包括验证、状态推进和钩子处理。
    """
    def __init__(self, agent: Any):
        """初始化执行器，需要 Agent 实例来访问库和工具信息"""
        self.agent = agent

    async def pre_process(self, state: WorkflowState, user_message: Optional[UserMessage], messages: List[Message]) -> None:
        """
        处理用户消息，可能前进到工作流下一步或将输入发送给Python工作流。
        
        参数:
            state: 工作流状态
            user_message: 用户消息对象，可能为None（在user_ready消息中）
            messages: 完整消息历史
        """
        if not state.workflow_name:
            # 没有活动的工作流，不执行任何操作
            return
            
        # 对于user_ready消息，user_message为None，仅执行工作流自动前进
        if user_message is None:
            # 如果没有用户消息，仅可能是工作流初始化或user_ready消息
            logger.debug(f"工作流 pre_process: 收到user_ready消息，当前步骤类型={type(state.get_current_step()).__name__ if state.get_current_step() else 'None'}")
            return
        
        # 根据工作流来源类型处理
        if state.source_type == 'python' or state.source_type == 'remote':
            # --- Python 工作流特殊处理 ---
            logger.debug(f"Python 工作流 pre_process: 等待状态={state.is_waiting_for_user}, 当前步骤类型={type(state.get_current_step()).__name__ if state.get_current_step() else 'None'}")
            
            # 如果当前步骤是UserInputStep或工作流正在等待用户输入
            current_step = state.get_current_step()
            if current_step and isinstance(current_step, UserInputStep):
                try:
                    # 将用户消息发送到生成器并获取下一步
                    await state.advance_python_step(user_message)
                    logger.info("向Python工作流发送用户输入并前进到下一步")
                    
                except StopAsyncIteration:
                    # 生成器已完成，无需处理
                    logger.info("Python工作流在接收用户输入后已完成")
                    return
                except Exception as e:
                    # 记录错误但继续进行
                    logger.error(f"向Python工作流发送用户输入时出错: {e}")
                    logger.debug(traceback.format_exc())
            
            # 如果只是等待标志而没有UserInputStep(旧机制兼容)
            elif state.is_waiting_for_user:
                try:
                    # 将用户消息发送到生成器并获取下一步
                    await state.advance_python_step(user_message)
                    logger.info("向Python工作流发送用户输入并前进到下一步(使用旧机制)")
                    
                except StopAsyncIteration:
                    # 生成器已完成，无需处理
                    logger.info("Python工作流在接收用户输入后(旧机制)已完成")
                    return
                except Exception as e:
                    # 记录错误但继续进行
                    logger.error(f"向Python工作流发送用户输入时出错(旧机制): {e}")
                    logger.debug(traceback.format_exc())
            
        elif state.source_type == 'yaml':
            # --- YAML 工作流常规处理 ---
            # 检查是否在GenerateStep上，且wait_user=True
            current_step = state.get_current_step()
            if current_step:
                # 检查是否是UserInputStep
                if isinstance(current_step, UserInputStep):
                    # 执行下一步
                    next_idx = current_step.index + 1 if current_step.index is not None else None
                    if next_idx is not None:
                        state.advance_step(next_idx)
                        logger.info(f"YAML工作流在用户输入后前进到步骤 {next_idx}")
                # 兼容旧的GenerateStep等待用户逻辑
                elif isinstance(current_step, GenerateStep) and current_step.wait_user:
                    # 执行下一步
                    if current_step.next is not None:
                        state.advance_step(current_step.next)
                        logger.info(f"YAML工作流在用户输入后前进到步骤 {current_step.next} (使用旧机制)")
        
        # 其他类型将被忽略

    async def post_process(self, state: WorkflowState, assistant_message: AssistantMessage, messages: List[Message], library_name: str) -> Tuple[bool, List[Message]]:
        """
        处理LLM生成的回复，决定是否需要重新生成。
        
        参数:
            state: 工作流状态
            assistant_message: 助手消息对象
            messages: 消息对象列表
            library_name: 工作流库名称
            
        返回:
            (是否需要重新生成, 需要添加的消息列表)
        """
        current_step_index = state.step_index
        current_step = state.get_current_step()

        if not current_step:
            raise RuntimeError(f"post_process: 找不到索引为 {current_step_index} 的步骤")

        tool_calls_raw = assistant_message.tool_calls
        tool_calls = tool_calls_raw if isinstance(tool_calls_raw, list) else []

        # 验证当前步骤的输出
        validation_passed, error_message, validation_details = self._validate_step(state, assistant_message, tool_calls, library_name)

        # 处理验证失败
        if not validation_passed:
            logger.warning(f"步骤 {current_step_index} ({current_step.operation}) 验证失败: {error_message}")
            
            # 根据步骤类型生成不同的错误消息
            message_content = f"你的输出不符合当前步骤要求。{error_message}"
            
            # UserInputStep 的特殊错误消息
            if isinstance(current_step, UserInputStep):
                message_content = f"当前是等待用户输入的步骤。{error_message} 请简洁提示用户，然后等待用户回应。"
            # 对 EXECUTE 步骤使用更具体的错误消息
            elif isinstance(current_step, ExecuteStep):
                execute_error_desc = validation_details.get('expected_action_description', '')
                if execute_error_desc and not execute_error_desc.startswith("[错误"):
                    message_content = f"你的工具调用不符合当前步骤要求。{execute_error_desc}"

            # 创建消息对象
            msg = AssistantMessage(content=message_content)
            # 返回需要重新生成和需要添加的消息列表
            return True, [msg]

        # 验证通过，执行后处理和自动前进
        called_tool_details = validation_details.get("called_tool_details", {}) # {tool_name: group_index}
        
        # 针对UserInputStep的特殊处理
        if isinstance(current_step, UserInputStep):
            # UserInputStep无需执行后处理和自动前进，等待用户输入
            logger.debug("当前是UserInputStep，验证通过，等待用户输入")
            # 确保状态设置正确
            if state.source_type == 'python' or state.source_type == 'remote':
                # 不需要在这里设置is_waiting_for_user，已在advance_python_step_next中设置
                pass
            return False, []
        
        # 其他步骤类型的正常处理
        self._post_validation_processing(state, current_step, called_tool_details, library_name)
        await self._advance_step_if_needed(state, current_step, called_tool_details, library_name, assistant_message)

        # 验证通过，不需要添加任何消息
        return False, []

    def _validate_step(self, state: WorkflowState, assistant_message: AssistantMessage, tool_calls: List[Any], library_name: str) -> Tuple[bool, str, Dict[str, Any]]:
        """根据当前步骤类型调用具体的私有验证方法"""
        # 统一使用 state.get_current_step()
        current_step = state.get_current_step()
        validation_details = {} # 用于存储特定验证类型的附加信息，如 called_tool_details

        # 检查 current_step 是否有效
        if not current_step:
             logger.warning(f"_validate_step: 当前步骤为空，无法验证。状态: {state}")
             # 如果没有当前步骤，通常意味着工作流结束或未初始化，验证通过？或失败？
             # 暂时返回验证通过，让后续逻辑处理（如无步骤不执行任何操作）
             return True, "", validation_details

        # --- 根据步骤类型进行特定验证 ---
        if isinstance(current_step, ExecuteStep):
            # 验证 EXECUTE 步骤
            validation_result = self._validate_execute_step(state, tool_calls, library_name)
            passed = validation_result["validation_passed"]
            error_msg = validation_result.get('expected_action_description', "") if not passed else ""
            called_tool_details = validation_result.get('called_tool_details', {})
            return passed, error_msg, {"called_tool_details": called_tool_details}

        elif isinstance(current_step, ConditionStep):
            # 验证 CONDITION 步骤
            is_valid, error_msg = self._validate_condition_step(current_step, tool_calls, library_name)
            return is_valid, error_msg, {}

        elif isinstance(current_step, UserInputStep):
            # 验证 USER_INPUT 步骤
            is_valid, error_msg = self._validate_user_input_step(current_step, assistant_message, tool_calls, state)
            return is_valid, error_msg, {}

        elif isinstance(current_step, SwitchStep):
            # 验证 SWITCH 步骤
            is_valid, error_msg = self._validate_switch_step(current_step, tool_calls, library_name)
            return is_valid, error_msg, {}

        elif isinstance(current_step, GenerateStep):
            # 验证 GENERATE 步骤
            is_valid, error_msg = self._validate_generate_step(current_step, assistant_message, tool_calls)
            return is_valid, error_msg, {}

        elif isinstance(current_step, JumpStep):
            passed, error_msg = self._validate_jump_step(current_step, tool_calls, library_name)
            return passed, error_msg, {}
        elif isinstance(current_step, NopStep):
            passed, error_msg = self._validate_nop_step(current_step, tool_calls, library_name)
            return passed, error_msg, {}
        elif isinstance(current_step, HaltStep):
            passed, error_msg = self._validate_halt_step(current_step, assistant_message, tool_calls)
            return passed, error_msg, {}
        else:
            # 如果 current_step 不是预期的 WorkflowStep 子类
            logger.error(f"_validate_step: 遇到未知的步骤对象类型 {type(current_step)}，无法验证。")
            # 这种情况通常是内部错误，返回验证失败
            return False, f"[内部错误] 未知的步骤类型: {type(current_step)}", validation_details

    # --- 新增用户输入步骤验证方法 ---
    def _validate_user_input_step(self, step: UserInputStep, assistant_message: AssistantMessage, tool_calls: List[Any], state: WorkflowState) -> Tuple[bool, str]:
        """
        验证USER_INPUT步骤的模型输出。
        
        USER_INPUT步骤是等待用户输入的状态，模型不应该生成内容或调用工具。
        对于Python工作流，当前步骤应该是UserInputStep类型。
        
        参数:
            step: UserInputStep对象
            assistant_message: 助手消息对象
            tool_calls: 工具调用列表
            state: 工作流状态对象
            
        返回:
            (验证是否通过, 错误消息)
        """
        # 检查是否有工具调用
        if tool_calls and len(tool_calls) > 0:
            error_message = "当前是等待用户输入的步骤，你不应该调用任何工具。请等待用户的回应。"
            return False, error_message
            
        # 检查是否有消息内容
        if assistant_message.content and assistant_message.content.strip():
            # 给一定的宽容度，允许模型提示用户输入，但不能过长
            content_length = len(assistant_message.content.strip())
            if content_length > 300:  # 调整阈值，允许适当的提示文本
                error_message = "当前是等待用户输入的步骤，你的回复过长。请简洁地提示用户输入，然后等待用户的回应。"
                return False, error_message
        
        # 检查Python工作流状态一致性
        if state.source_type == 'python' or state.source_type == 'remote':
            current_step = state.get_current_step()
            if not isinstance(current_step, UserInputStep):
                logger.warning(f"_validate_user_input_step被调用，但当前步骤不是UserInputStep: {type(current_step)}")
                # 不返回错误，因为这可能是内部状态问题，不是模型的问题
        
        return True, ""

    def _post_validation_processing(self, state: WorkflowState, current_step: WorkflowStep, called_tool_details: Dict[str, int], library_name: str) -> None:
        """
        验证通过后的处理，更新 EXECUTE 步骤对应动作组的计数器。
        适配 YAML (index) 和 Python (name) 的步骤标识符。
        
        参数:
            state: 当前工作流状态
            current_step: 当前步骤对象 (必须是 ExecuteStep)
            called_tool_details: 字典，映射已调用工具名到其所属动作组索引
            library_name: 工作流库名称
        """
        if not isinstance(current_step, ExecuteStep):
            # 如果不是 ExecuteStep，则无需处理计数
            return
            
        workflow_control_calls = {f"{library_name}.next_step", f"{library_name}.condition_branch"}
        # 遍历已调用的非控制流工具及其所属组索引
        for tool_name, group_index in called_tool_details.items():
            if tool_name not in workflow_control_calls:
                # 调用 state 的 increment_action_group_count 方法
                # 该方法内部已处理 YAML/Python 的 step_id 差异
                state.increment_action_group_count(group_index)

    # --- 修改自动前进逻辑 ---
    async def _advance_step_if_needed(self, state: WorkflowState, current_step: WorkflowStep, called_tool_details: Dict[str, int], library_name: str, assistant_message: AssistantMessage = None) -> None:
        """
        根据当前步骤类型和条件判断是否自动前进。
        适配 YAML 和 Python 工作流。

        参数:
            state: 当前工作流状态
            current_step: 当前步骤对象
            called_tool_details: 字典，映射已调用工具名到其所属动作组索引
            library_name: 工作流库名称
            assistant_message: 助手消息对象，用于获取生成的内容
        """
        # 检查是否调用了 next_step 或 condition_branch，如果是，则不自动前进
        called_control_tool = False
        for tool_name in called_tool_details:
            if tool_name == f"{library_name}.next_step" or tool_name == f"{library_name}.condition_branch":
                called_control_tool = True
                break
        if called_control_tool:
            logger.debug("调用了控制工具，跳过自动前进。")
            return
            
        # --- 根据步骤类型判断是否需要自动前进 --- 
        should_auto_advance = False
        next_target_yaml = None # 仅用于 YAML

        # UserInputStep 从不自动前进，必须等待用户输入
        if isinstance(current_step, UserInputStep):
            logger.debug(f"当前是UserInputStep，不自动前进，等待用户输入")
            return
            
        # HaltStep 立即终止工作流并发送完成通知
        if isinstance(current_step, HaltStep):
            workflow_name = state.workflow_name or "unknown_workflow"
            logger.info(f"遇到 HaltStep，工作流 '{workflow_name}' 立即终止")
            
            # 清理状态并发送完成通知
            state.clear()
            await self._send_workflow_completion_via_agent(workflow_name)
            return

        if isinstance(current_step, ExecuteStep):
            # 获取步骤 ID (index for YAML, name for Python)
            step_id = current_step.index if state.source_type == 'yaml' else current_step.name
            if step_id is None:
                 logger.error(f"无法检查 ExecuteStep 的自动前进：缺少标识符 (index/name)。步骤: {current_step}")
                 return
                 
            next_target_yaml = current_step.next # YAML 的目标
            if state.source_type == 'yaml' and next_target_yaml is None:
                 logger.warning(f"YAML ExecuteStep {step_id} 未定义 'next'，无法自动前进。")
                 return

            # 检查是否所有动作组的 max_calls 都满足
            all_max_calls_met = True
            if not current_step.actions: # 如果没有动作组，默认满足
                 all_max_calls_met = True
            else:
                action_group_counts = state.action_group_call_counts.get(step_id, {})
                for i, action_group in enumerate(current_step.actions):
                    group_call_count = action_group_counts.get(i, 0)
                    if group_call_count < action_group.max_calls:
                        all_max_calls_met = False
                        break
            
            if all_max_calls_met:
                 logger.debug(f"ExecuteStep '{step_id}' 的 max_calls 已满足。")
                 should_auto_advance = True
                 
        elif isinstance(current_step, GenerateStep):
             # GenerateStep总是自动前进到下一步，除非配置了wait_user(旧机制)
             if getattr(current_step, 'wait_user', False):
                 logger.debug(f"当前是GenerateStep，配置了wait_user=True，不自动前进 (旧机制)")
                 return

             logger.debug(f"当前是GenerateStep，准备自动前进")
             should_auto_advance = True

             # 检查是否需要将生成的内容传递给工作流
             generated_content = None
             if getattr(current_step, 'return_content', True) and state.source_type in ('python', 'remote'):
                 # 获取生成的内容（从assistant_message中提取）
                 if assistant_message and assistant_message.content:
                     generated_content = assistant_message.content
                     logger.debug(f"GenerateStep 将返回生成的内容: {len(generated_content)} 个字符")
                 else:
                     logger.warning(f"GenerateStep 配置了 return_content=True 但没有生成内容")

             if state.source_type == 'yaml':
                  next_target_yaml = current_step.next
                  if next_target_yaml is None:
                      logger.warning(f"YAML GenerateStep 未定义 'next'，无法自动前进。")
                      should_auto_advance = False # 没有下一步，无法前进
        
        elif isinstance(current_step, NopStep) or isinstance(current_step, JumpStep):
             # NOP和无条件JUMP已经在前面验证中要求手动调用next_step，这里不自动前进
             return

        # --- 执行自动前进 --- 
        if should_auto_advance:
             step_display = current_step.name or current_step.index
             logger.info(f"从步骤 '{step_display}' ({current_step.operation}) 自动前进...")
             try:
                 if state.source_type == 'yaml':
                     if next_target_yaml is not None:
                         state.advance_step(next_target_yaml)
                         logger.info(f"YAML 工作流前进到步骤 {state.step_index}。")
                     else:
                          # 前面已经检查过，理论上不会到这里
                          logger.error(f"无法自动前进 YAML 步骤 '{step_display}'：next 目标为 None。")
                 elif state.source_type == 'python' or state.source_type == 'remote':
                      # 对于GenerateStep，如果有生成的内容，则传递给工作流
                      if isinstance(current_step, GenerateStep) and generated_content is not None:
                          await state.advance_python_step(generated_content)
                          logger.info(f"Python 工作流前进并传递生成的内容: {len(generated_content)} 个字符")
                      else:
                          await state.advance_python_step()

                      new_step = state.get_current_step()
                      # 检查是否前进到了UserInputStep，如果是则标记等待用户输入
                      if new_step and isinstance(new_step, UserInputStep):
                          logger.info(f"Python 工作流前进到 UserInputStep '{new_step.name}'，等待用户输入。")
                      else:
                          logger.info(f"Python 工作流前进到步骤 '{new_step.name if new_step else '[已结束]'}'。")
                          
                 else:
                      logger.error(f"无法自动前进：未知的 source type '{state.source_type}'。")
                      
             except StopAsyncIteration:
                  workflow_name = state.workflow_name
                  logger.info(f"Python 工作流 '{workflow_name}' 在自动前进期间结束。")
                  # State 已在 advance_python_step 中处理结束状态
                  
                  # 通过 agent 查找 WorkflowLibrary 并发送完成通知
                  await self._send_workflow_completion_via_agent(workflow_name)
             except Exception as e:
                  logger.error(f"从步骤 '{step_display}' 自动前进时出错：{e}")
                  logger.debug(traceback.format_exc())
                  # 自动前进失败，可能需要停止工作流或发出警告？
                  # 暂时只记录错误

    # --- 私有验证方法 (从原模块级函数转换) ---

    def _validate_execute_step(self, state: WorkflowState, tool_calls: List[Any], library_name: str) -> Dict[str, Any]:
        """验证EXECUTE步骤的工具调用是否符合要求（适配动作组和状态）"""
        current_step = state.get_current_step()
        # 使用 state 获取计数器
        action_group_call_counts = state.action_group_call_counts

        if not isinstance(current_step, ExecuteStep):
             logger.error(f"_validate_execute_step 内部错误：期望 ExecuteStep，收到 {type(current_step)}")
             return {"validation_passed": False, "expected_action_description": "[内部错误]", "called_tool_details": {}}

        # 存储调用细节: {tool_name: group_index}
        called_tool_details: Dict[str, int] = {}
        expected_action_description = ""
        validation_passed = False
        # 获取步骤 ID (index for YAML, name for Python)
        step_id = current_step.index if state.source_type == 'yaml' else current_step.name
        if step_id is None:
             step_display_temp = current_step.name or current_step.index or "[未知]"
             logger.error(f"ExecuteStep '{step_display_temp}' 缺少标识符 (index/name)。无法正确验证计数。")
             # 返回错误，因为无法可靠验证
             return {"validation_passed": False, "expected_action_description": f"[内部错误] 步骤 '{step_display_temp}' 缺少标识符", "called_tool_details": {}}
             
        step_name_or_index = current_step.name or step_id # 用于日志和错误消息

        # 检查当前步骤的动作组 (ActionDefinition 列表)
        if not current_step.actions:
            # 如果没有定义任何动作组
            # 对于 YAML，通常意味着应该调用 next_step (如果定义了 next)
            # 对于 Python，取决于生成器逻辑，但通常意味着不期望调用工具？或者也调用 next_step?
            # 统一行为：如果没有动作组，只允许调用 next_step
            only_next_step_called = (len(tool_calls) == 1 and tool_calls[0].function.name == f"{library_name}.next_step")
            no_calls = not tool_calls

            if only_next_step_called:
                 validation_passed = True
                 called_tool_details = {f"{library_name}.next_step": -1} # 使用 -1 表示 next_step 不属于特定组
            elif no_calls:
                 validation_passed = False # 没有动作组定义，必须调用 next_step 前进 (假设总有下一步或结束)
                 expected_action_description = f"无指定动作组，必须调用 {library_name}.next_step() 前进"                 
            else: # 调用了非 next_step 工具
                 validation_passed = False
                 logger.error(f"工作流错误：EXECUTE 步骤 '{step_name_or_index}' 未定义任何动作组，但调用了非 next_step 工具。")
                 expected_action_description = f"[错误：EXECUTE 步骤 '{step_name_or_index}' 无动作组定义，只允许调用 next_step]"
        else:
            # 存在动作组定义
            # 获取当前步骤各动作组的调用计数 (使用 step_id)
            current_group_counts = action_group_call_counts.get(step_id, {})
            has_unmet_min_calls_group = False
            required_groups_indices: List[int] = [] # 记录需要调用的组索引
            optional_groups_indices: List[int] = [] # 记录可选的组索引

            # 遍历所有动作组，检查 min_calls 和 max_calls 状态
            for i, action_group in enumerate(current_step.actions):
                count = current_group_counts.get(i, 0)
                if count < action_group.min_calls:
                    has_unmet_min_calls_group = True
                    required_groups_indices.append(i)
                if count < action_group.max_calls:
                    optional_groups_indices.append(i)

            # 生成期望动作描述 (基于动作组)
            def get_group_description(group_index: int) -> str:
                group = current_step.actions[group_index]
                names_str = ", ".join(f"`{name}`" for name in group.names)
                return f"组 {group_index} ({names_str})"

            if has_unmet_min_calls_group:
                required_desc = ", ".join(get_group_description(i) for i in required_groups_indices)
                expected_action_description = f"必须从以下未满足最小调用次数的动作组中调用工具：{required_desc}"
            elif optional_groups_indices:
                optional_desc = ", ".join(get_group_description(i) for i in optional_groups_indices)
                expected_action_description = f"可以继续从以下动作组调用工具：{optional_desc}，或调用 `{library_name}.next_step()` 前进到下一步"
            else:
                expected_action_description = f"已达到所有动作组的调用上限，应调用 `{library_name}.next_step()` 前进到下一步"

            # 验证工具调用
            all_tools_valid = True
            if tool_calls:
                for tool_call in tool_calls:
                    called_tool = tool_call.function.name
                    is_next_step_call = (called_tool == f"{library_name}.next_step")

                    if is_next_step_call:
                        # 检查 next_step 调用的时机
                        if has_unmet_min_calls_group:
                            all_tools_valid = False
                            required_desc = ", ".join(get_group_description(i) for i in required_groups_indices)
                            expected_action_description = f"还有未满足最小调用次数的动作组 ({required_desc})，不能调用 {library_name}.next_step()"
                            logger.warning(f"工具验证失败：调用了 next_step，但还有未满足最小调用次数的动作组: {required_desc}")
                            break # 一个无效即整体无效
                        else:
                            # next_step 调用有效，记录但不关联特定组
                            called_tool_details[called_tool] = -1 
                    else:
                        # 查找调用工具所属的动作组索引
                        matching_group_index = self._find_matching_action_group_index(current_step, called_tool)
                        
                        if matching_group_index is None:
                            # 工具不属于任何定义的动作组
                            all_tools_valid = False
                            allowed_groups_desc = ", ".join(get_group_description(i) for i, _ in enumerate(current_step.actions))
                            expected_action_description = f"工具 `{called_tool}` 不属于任何允许的动作组 ({allowed_groups_desc})"
                            logger.warning(f"工具验证失败：工具 '{called_tool}' 不在允许的动作组内")
                            break
                        
                        # 检查该动作组是否已达上限
                        group_count = current_group_counts.get(matching_group_index, 0)
                        group_max_calls = current_step.actions[matching_group_index].max_calls
                        if group_count >= group_max_calls:
                            all_tools_valid = False
                            group_desc = get_group_description(matching_group_index)
                            expected_action_description = f"动作组 {group_desc} 已达到最大调用次数 ({group_max_calls})，不能再调用 `{called_tool}`"
                            logger.warning(f"工具验证失败：动作组 {group_desc} 已达最大调用次数")
                            break
                        
                        # 记录有效调用及其所属组
                        called_tool_details[called_tool] = matching_group_index

                # 如果验证失败，提前退出循环
                if not all_tools_valid:
                     pass # all_tools_valid 已设为 False

            else: # No tool calls
                # 如果期望调用 next_step 但没有调用，则无效
                if not optional_groups_indices and not has_unmet_min_calls_group:
                     all_tools_valid = False
                     expected_action_description = f"已达到所有动作组调用上限，必须调用 {library_name}.next_step()"
                # 如果期望调用其他工具但没有调用，则无效
                elif has_unmet_min_calls_group:
                     all_tools_valid = False
                     required_desc = ", ".join(get_group_description(i) for i in required_groups_indices)
                     expected_action_description = f"必须从以下动作组调用工具：{required_desc}"
                # ExecuteStep 必须有工具调用或next_step调用，不能只生成文本
                else:
                     all_tools_valid = False
                     optional_desc = ", ".join(get_group_description(i) for i in optional_groups_indices)
                     expected_action_description = f"当前是 EXECUTE 步骤，必须调用工具。可以从以下动作组调用工具：{optional_desc}，或调用 `{library_name}.next_step()` 前进到下一步"

            validation_passed = all_tools_valid

            # 如果调用了 next_step 且验证通过，确保它是唯一调用的
            if validation_passed and f"{library_name}.next_step" in called_tool_details:
                 called_tool_details = {f"{library_name}.next_step": -1}

        # 返回验证结果，包含 called_tool_details
        return {
            "validation_passed": validation_passed,
            "expected_action_description": expected_action_description,
            "called_tool_details": called_tool_details
        }

    def _validate_condition_step(self, step: ConditionStep, tool_calls: List[Any], library_name: str) -> Tuple[bool, str]:
        """验证CONDITION步骤的工具调用"""
        condition_branch_called = False
        for tool_call in tool_calls:
            if tool_call.function.name == f"{library_name}.condition_branch":
                condition_branch_called = True
                break
        if not condition_branch_called:
            condition_desc = step.condition_description
            error_message = f"当前是CONDITION步骤，你必须评估条件 \"{condition_desc}\" 并调用 `{library_name}.condition_branch(condition_result=True/False)` 以进行分支选择。"
            return False, error_message
        return True, ""

    def _validate_generate_step(self, step: GenerateStep, assistant_message: AssistantMessage, tool_calls: List[Any]) -> Tuple[bool, str]:
        """验证GENERATE步骤的输出"""
        if tool_calls and len(tool_calls) > 0:
            error_message = "当前是GENERATE步骤，你应该直接生成文本回复，不要调用任何工具。请删除所有工具调用，直接生成回复文本。"
            return False, error_message
        if not assistant_message.content:
            error_message = "当前是GENERATE步骤，你需要生成有内容的文本回复。请生成相关内容。"
            return False, error_message
        return True, ""

    def _validate_jump_step(self, step: JumpStep, tool_calls: List[Any], library_name: str) -> Tuple[bool, str]:
        """验证JUMP步骤的工具调用"""
        if step.next_condition:
            condition_branch_called = False
            for tool_call in tool_calls:
                if tool_call.function.name == f"{library_name}.condition_branch":
                    condition_branch_called = True
                    break
            if not condition_branch_called:
                condition_desc = step.condition_description
                error_message = f"当前是带条件的JUMP步骤，你必须评估条件 \"{condition_desc}\" 并调用 `{library_name}.condition_branch(condition_result=True/False)` 以进行分支选择。"
                return False, error_message
        else:
            next_step_called = False
            for tool_call in tool_calls:
                if tool_call.function.name == f"{library_name}.next_step":
                    next_step_called = True
                    break
            if not next_step_called:
                error_message = f"当前是无条件JUMP步骤，你必须调用 `{library_name}.next_step()` 以前进到下一步。"
                return False, error_message
        return True, ""

    def _validate_nop_step(self, step: NopStep, tool_calls: List[Any], library_name: str) -> Tuple[bool, str]:
        """验证NOP步骤的工具调用"""
        next_step_called = False
        for tool_call in tool_calls:
            if tool_call.function.name == f"{library_name}.next_step":
                next_step_called = True
                break
        if not next_step_called:
            error_message = f"当前是NOP步骤，你必须调用 `{library_name}.next_step()` 以前进到下一步。"
            return False, error_message
        return True, ""

    def _validate_halt_step(self, step: HaltStep, assistant_message: AssistantMessage, tool_calls: List[Any]) -> Tuple[bool, str]:
        """验证HALT步骤的输出"""
        if tool_calls and len(tool_calls) > 0:
            error_message = "当前是HALT步骤，工作流已结束，你应该直接生成最终的文本回复，不要调用任何工具。"
            return False, error_message
        if not assistant_message.content:
            error_message = "当前是HALT步骤，工作流已结束，你需要生成最终的总结或结论作为回复。"
            return False, error_message
        return True, ""

    # --- 私有辅助方法 (从原模块级函数转换) ---

    def _is_library_reference(self, name: str) -> bool:
        """判断一个名称是否是有效的、已知的库引用。"""
        # 检查 self.agent 是否存在且有效
        if not self.agent or not hasattr(self.agent, 'libraries') or not isinstance(self.agent.libraries, dict):
             logger.warning("_is_library_reference 检查失败: self.agent 或 self.agent.libraries 无效。")
             return False
        return "." not in name and name in self.agent.libraries

    async def _send_workflow_completion_via_agent(self, workflow_name: str):
        """
        通过 agent 查找 WorkflowLibrary 并发送工作流完成通知。
        
        参数:
            workflow_name: 完成的工作流名称
        """
        if not self.agent or not workflow_name:
            return
            
        try:
            # 查找 WorkflowLibrary 实例
            workflow_library = None
            for library_name, library in self.agent.libraries.items():
                if hasattr(library, '_send_workflow_completion_notification'):
                    workflow_library = library
                    break
            
            if workflow_library:
                # 发送完成通知
                await workflow_library._send_workflow_completion_notification(workflow_name)
            else:
                logger.warning("未找到 WorkflowLibrary 实例，无法发送工作流完成通知")
        except Exception as e:
            logger.error(f"通过 agent 发送工作流完成通知失败: {e}")
            logger.debug(traceback.format_exc())

    def _find_matching_action_group_index(self, step: ExecuteStep, tool_name: str) -> Optional[int]:
        """
        查找与给定工具名称匹配的动作组 (ActionDefinition) 的索引。
        
        参数:
            step: 当前 ExecuteStep 对象
            tool_name: 被调用的工具名称
        
        返回:
            匹配的动作组在 step.actions 列表中的索引 (int)，如果未找到则返回 None。
        """
        # 遍历步骤中的每个动作组 (ActionDefinition) 及其索引
        for i, action_group in enumerate(step.actions):
            # 遍历动作组中的每个名称 (工具名或库名)
            for name_in_group in action_group.names:
                # 直接匹配工具名
                if name_in_group == tool_name:
                    return i
                # 检查是否是库引用，并且工具名以库名加点开头
                if self._is_library_reference(name_in_group) and tool_name.startswith(name_in_group + '.'):
                    return i
        # 如果遍历完所有动作组都没有找到匹配项
        return None

    def _validate_switch_step(self, step: SwitchStep, tool_calls: List[Any], library_name: str) -> Tuple[bool, str]:
        """
        验证 SWITCH 步骤的工具调用。
        LLM 应该调用 workflow.select_case_target 来选择一个分支。
        
        参数:
            step: SwitchStep 实例
            tool_calls: LLM 的工具调用列表
            library_name: 当前库名称
            
        返回:
            (是否通过验证, 错误消息)
        """
        # SWITCH 步骤必须恰好调用一个 select_case_target 工具
        expected_tool = f"{library_name}.select_case_target"
        
        if not tool_calls:
            available_cases = [f"'{case.name}': {case.description}" for case in step.cases]
            error_msg = (
                f"SWITCH 步骤 '{step.name or step.index}' 要求你选择一个分支。\n"
                f"可选分支:\n" + "\n".join([f"  - {case}" for case in available_cases]) + "\n"
                f"请调用工具 {expected_tool}(target_case_name='你选择的分支名称')。"
            )
            return False, error_msg
        
        # 检查是否有多个工具调用
        if len(tool_calls) > 1:
            return False, f"SWITCH 步骤只允许调用一个工具，但检测到 {len(tool_calls)} 个工具调用。"
        
        # 检查工具调用是否正确
        tool_call = tool_calls[0]
        function_name = tool_call.function.name if hasattr(tool_call, 'function') and hasattr(tool_call.function, 'name') else ''
        
        if function_name != expected_tool:
            available_cases = [f"'{case.name}': {case.description}" for case in step.cases]
            error_msg = (
                f"SWITCH 步骤 '{step.name or step.index}' 需要调用 {expected_tool}，"
                f"但你调用了 '{function_name}'。\n"
                f"可选分支:\n" + "\n".join([f"  - {case}" for case in available_cases]) + "\n"
                f"请调用 {expected_tool}(target_case_name='你选择的分支名称')。"
            )
            return False, error_msg
        
        # 验证参数
        try:
            import json
            arguments = tool_call.function.arguments if hasattr(tool_call, 'function') and hasattr(tool_call.function, 'arguments') else '{}'
            if isinstance(arguments, str):
                args_dict = json.loads(arguments)
            else:
                args_dict = arguments
            
            target_case_name = args_dict.get('target_case_name', '')
            if not target_case_name:
                return False, f"调用 {expected_tool} 时缺少必需的参数 'target_case_name'。"
            
            # 检查选择的分支是否存在
            valid_case_names = [case.name for case in step.cases]
            if target_case_name not in valid_case_names:
                available_cases = [f"'{case.name}': {case.description}" for case in step.cases]
                error_msg = (
                    f"选择的分支 '{target_case_name}' 不存在。\n"
                    f"可选分支:\n" + "\n".join([f"  - {case}" for case in available_cases]) + "\n"
                    f"请从中选择一个有效的分支名称。"
                )
                return False, error_msg
                
        except (json.JSONDecodeError, AttributeError) as e:
            return False, f"解析 {expected_tool} 的参数时出错: {e}"
        
        return True, ""