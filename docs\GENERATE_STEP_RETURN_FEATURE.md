# GenerateStep 返回内容功能

## 概述

GenerateStep 现在支持将生成的内容直接返回给工作流，使得工作流能够获取和处理 LLM 生成的文本内容。这个功能让工作流能够：

1. 获取生成的内容并存储到寄存器中
2. 基于生成的内容进行后续处理
3. 支持多轮生成和内容迭代
4. 为未来的结构化数据返回奠定基础

## 新增功能

### GenerateStep 新增字段

```python
class GenerateStep(WorkflowStepBase):
    operation: Literal[WorkflowOperation.GENERATE] = WorkflowOperation.GENERATE
    content_description: str = "生成相应的文本内容"
    wait_user: bool = False
    next: Optional[int] = None
    return_content: bool = True  # 新增：是否将生成的内容返回给工作流
```

### WorkflowContext.generate() 方法更新

```python
async def generate(
    self,
    description: str,
    wait_user: bool = False,
    name: Optional[str] = None,
    return_content: bool = True  # 新增参数
) -> Optional[str]:  # 新增返回值
    """
    执行一个 GENERATE 步骤。
    
    参数:
        description: 生成内容的描述
        wait_user: 是否在生成后等待用户输入
        name: 可选的步骤名称
        return_content: 是否将生成的内容返回给工作流
        
    返回:
        如果 return_content=True，返回生成的文本内容；否则返回 None
    """
```

## 使用示例

### 基础用法

```python
async def steps(ctx: WorkflowContext) -> AsyncGenerator[WorkflowStep, Any]:
    # 生成内容并获取返回值
    generated_content = await ctx.generate(
        name="generate_summary",
        description="生成一个简短的摘要",
        return_content=True  # 返回生成的内容
    )
    
    # 将生成的内容存储到寄存器
    ctx.set_register("summary", generated_content)
    
    # 基于生成的内容进行后续处理
    detailed_content = await ctx.generate(
        name="expand_summary",
        description=f"基于以下摘要生成详细内容：\n{generated_content}",
        return_content=True
    )
    
    # 最终回复给用户（不返回内容）
    await ctx.generate(
        name="final_response",
        description=f"向用户展示最终结果：{detailed_content}",
        return_content=False  # 直接作为回复，不返回内容
    )
```

### 多轮对话示例

```python
async def steps(ctx: WorkflowContext) -> AsyncGenerator[WorkflowStep, Any]:
    user_message: UserMessage = yield UserInputStep(
        name="get_user_input",
        description="等待用户输入"
    )
    
    # 第一轮生成：分析用户需求
    analysis = await ctx.generate(
        name="analyze_request",
        description="分析用户的需求和意图",
        return_content=True
    )
    
    # 第二轮生成：制定解决方案
    solution = await ctx.generate(
        name="create_solution",
        description=f"基于分析结果制定解决方案：\n{analysis}",
        return_content=True
    )
    
    # 第三轮生成：向用户展示结果
    await ctx.generate(
        name="present_solution",
        description=f"向用户展示分析和解决方案：\n分析：{analysis}\n方案：{solution}",
        return_content=False
    )
```

## 远程工作流支持

远程工作流客户端也完全支持新功能：

```python
# 远程工作流中的用法
async def remote_workflow_steps(session: WorkflowSession):
    # 生成内容并获取返回值
    content = await session.generate(
        description="生成内容",
        return_content=True
    )
    
    # 处理生成的内容
    processed_content = process_content(content)
    
    # 最终回复
    await session.generate(
        description=f"展示处理结果：{processed_content}",
        return_content=False
    )
```

## 向后兼容性

- 默认 `return_content=True`，保持现有行为
- 现有工作流无需修改即可继续工作
- 旧的 `wait_user` 机制仍然支持

## 未来扩展

这个架构为未来支持结构化数据返回奠定了基础：

```python
# 未来可能的扩展
structured_data = await ctx.generate(
    description="生成JSON格式的数据",
    return_content=True,
    return_format="json"  # 未来可能的参数
)

# 或者返回自定义对象
custom_object = await ctx.generate(
    description="生成自定义数据结构",
    return_content=True,
    return_type=CustomDataClass  # 未来可能的参数
)
```

## 实现细节

1. **WorkflowExecutor** 在处理 GenerateStep 时会检查 `return_content` 字段
2. 如果 `return_content=True`，会将 `assistant_message.content` 传递给 `advance_python_step()`
3. **StepCommunicator** 负责在 WorkflowContext 和包装器生成器之间传递结果
4. 远程工作流通过 WebSocket 消息传递生成的内容

这个设计现代、简洁、优雅，为工作流系统提供了强大的内容生成和处理能力。
