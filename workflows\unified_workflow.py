from typing import Dict, Any, AsyncGenerator, TYPE_CHECKING
from libraries.workflow_context import WorkflowContext
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep, Case
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "统一工作流，可根据用户输入选择不同的处理分支"
INITIAL_REGISTERS: Dict[str, Any] = {}


# --- 工作流生成器函数 ---
async def steps(ctx: 'WorkflowContext'):
    """
    统一工作流主入口。
    等待用户输入后，根据用户意图选择对应的处理分支。
    """

    user_message: 'UserMessage' = await ctx.user_input(
        name="user_input",
        description="等待用户输入消息"
    )

    await ctx.switch(
        name="switch",
        description="根据用户的消息，选择不同的工作流",
        cases=[
            Case(
                name="search_news", 
                description="搜索新闻",
                handler=search_news
            ),
            Case(
                name="chat", 
                description="闲聊",
                handler=chat
            ),
        ]
    )
    
    await ctx.halt()

async def search_news(ctx: 'WorkflowContext'):
    """
    新闻搜索工作流分支。
    使用 Bing 搜索并分析新闻，提供结构化摘要。
    """
    # 运行时导入
    from libraries.workflow_models import ActionDefinition

    max_attempts = 3

    for i in range(max_attempts):
        # 步骤 1: 搜索和抓取新闻内容
        await ctx.execute(
            name="search_news",
            description="请使用Bing检索新闻",
            actions=[
                ActionDefinition(
                    names=["playwright"], 
                    min_calls=1, 
                    max_calls=5
                ),
            ],
        )

        # 步骤 2: 检查新闻信息收集是否充分
        is_task_complete: bool = await ctx.condition(
            name="check_news_complete",
            description="评估新闻信息收集完成度，检查是否已经获取到足够的新闻资讯，包括新闻的时效性、相关性和完整性",
        )

        # 如果新闻信息不够充分，继续搜索
        if is_task_complete:
            break

    # 步骤 3: 生成新闻摘要和分析
    summary = await ctx.generate(
        name="generate_news_summary",
        description="整理并分析收集到的新闻信息，生成结构化的新闻摘要、关键要点和相关分析",
    ) 



async def chat(ctx: 'WorkflowContext'):
    """
    聊天工作流分支。
    生成自然的对话回复。
    """

    await ctx.generate(
        name="generate_chat_response",
        description="根据用户的消息，生成回复",
    )

