#!/usr/bin/env python3
"""
测试 GenerateStep 返回内容功能的简单脚本。
"""

import asyncio
from typing import Dict, Any, AsyncGenerator
from libraries.workflow_context import WorkflowContext
from libraries.workflow_models import UserInputStep, WorkflowStep, GenerateStep
from message import UserMessage

# 模拟工作流元数据
WORKFLOW_DESCRIPTION = "测试GenerateStep返回功能"
INITIAL_REGISTERS: Dict[str, Any] = {}

async def test_generate_return_workflow(ctx: WorkflowContext) -> AsyncGenerator[WorkflowStep, Any]:
    """
    测试GenerateStep返回内容功能的工作流。
    """
    
    print("开始测试 GenerateStep 返回内容功能...")
    
    # 第一次生成：返回内容
    print("第一次生成：返回内容")
    content1 = await ctx.generate(
        name="test_generate_with_return",
        description="生成一个简短的问候语",
        return_content=True
    )
    
    print(f"生成的内容1: {content1}")
    assert content1 is not None, "应该返回生成的内容"
    
    # 将内容存储到寄存器
    ctx.set_register("greeting", content1)
    
    # 第二次生成：不返回内容
    print("第二次生成：不返回内容")
    content2 = await ctx.generate(
        name="test_generate_without_return",
        description="生成另一个问候语",
        return_content=False
    )
    
    print(f"生成的内容2: {content2}")
    assert content2 is None, "不应该返回内容"
    
    # 第三次生成：基于之前的内容
    print("第三次生成：基于之前的内容")
    previous_greeting = ctx.get_register("greeting")
    content3 = await ctx.generate(
        name="test_generate_based_on_previous",
        description=f"基于之前的问候语生成一个回应：{previous_greeting}",
        return_content=True
    )
    
    print(f"生成的内容3: {content3}")
    assert content3 is not None, "应该返回生成的内容"
    
    # 最终生成：向用户展示结果
    print("最终生成：向用户展示结果")
    await ctx.generate(
        name="final_response",
        description=f"向用户展示测试结果：\n原始问候：{content1}\n回应：{content3}",
        return_content=False
    )
    
    print("测试完成！")
    await ctx.halt()

def test_generate_step_model():
    """测试 GenerateStep 模型的新字段"""
    print("测试 GenerateStep 模型...")
    
    # 测试默认值
    step1 = GenerateStep(
        name="test_step",
        content_description="测试生成"
    )
    assert step1.return_content == True, "默认应该返回内容"
    
    # 测试显式设置
    step2 = GenerateStep(
        name="test_step2",
        content_description="测试生成2",
        return_content=False
    )
    assert step2.return_content == False, "应该不返回内容"
    
    print("GenerateStep 模型测试通过！")

if __name__ == "__main__":
    print("开始测试 GenerateStep 返回内容功能...")
    
    # 测试模型
    test_generate_step_model()
    
    print("所有测试通过！")
    print("\n注意：完整的工作流测试需要在 MeowAgent 环境中运行。")
    print("这个脚本只测试了模型和基本功能。")
