"""
远程工作流服务器模块。

该模块实现了 MeowAgent 内部的远程工作流支持，包括：
1. RemoteWorkflowConnectionManager - 管理 WebSocket 连接和工作流实例
2. create_proxy_generator - 创建代理生成器，桥接远程客户端和本地工作流系统
3. RemoteWorkflowSource - 远程工作流源定义
"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, AsyncGenerator, Callable
from fastapi import WebSocket, WebSocketDisconnect
from libraries.workflow_models import (
    WorkflowStep, ExecuteStep, ConditionStep, GenerateStep, 
    UserInputStep, SwitchStep, HaltStep, ActionDefinition, CaseDefinition, RemoteWorkflowSource, WorkflowSource
)
from libraries.workflow_remote_models import (
    WorkflowClientMessage, WorkflowServerMessage, RemoteStepDefinition, 
    MessageType, 
)
from message import UserMessage
from log import logger


class RemoteWorkflowConnectionManager:
    """远程工作流连接管理器（单例模式）"""
    
    _instance: Optional['RemoteWorkflowConnectionManager'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        # WebSocket 连接映射: workflow_instance_id -> WebSocket
        self._connections: Dict[str, WebSocket] = {}
        # 步骤请求队列映射: workflow_instance_id -> asyncio.Queue
        self._step_request_queues: Dict[str, asyncio.Queue] = {}
        # 结果发送队列映射: workflow_instance_id -> asyncio.Queue
        self._result_send_queues: Dict[str, asyncio.Queue] = {}
        # 已知的远程工作流脚本 - 简化为自动接受所有脚本名称
        self._known_remote_workflows: Dict[str, str] = {}
    
    @classmethod
    async def get_instance(cls) -> 'RemoteWorkflowConnectionManager':
        """获取单例实例（异步安全）"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    logger.info("RemoteWorkflowConnectionManager 单例实例已创建")
        return cls._instance
    
    @classmethod
    def get_instance_sync(cls) -> 'RemoteWorkflowConnectionManager':
        """获取单例实例（同步版本，用于向后兼容）"""
        if cls._instance is None:
            cls._instance = cls()
            logger.info("RemoteWorkflowConnectionManager 单例实例已创建（同步）")
        return cls._instance
    
    @property
    def connections(self) -> Dict[str, WebSocket]:
        """获取当前连接映射（只读）"""
        return self._connections.copy()
    
    @property 
    def registered_remote_workflows(self) -> Dict[str, str]:
        """获取已注册的远程工作流（只读）"""
        return self._known_remote_workflows.copy()
    
    def cleanup_connection(self, instance_id: str) -> None:
        """清理指定实例的连接（同步版本，供外部调用）"""
        self._connections.pop(instance_id, None)
        self._step_request_queues.pop(instance_id, None)
        self._result_send_queues.pop(instance_id, None)
        logger.info(f"已清理工作流实例: {instance_id}")
    
    async def handle_websocket_message(self, websocket: WebSocket, raw_message: str) -> Optional[str]:
        """处理来自WebSocket的原始消息，返回可能的workflow_instance_id"""
        try:
            message_data = json.loads(raw_message)
            client_message = WorkflowClientMessage(**message_data)
            
            if client_message.type == MessageType.START_WORKFLOW_INSTANCE:
                instance_id = await self._handle_start_workflow_instance(websocket, client_message)
                return instance_id
            elif client_message.type == MessageType.DEFINE_STEP:
                await self._handle_define_step(websocket, client_message)
                return None
            else:
                logger.warning(f"收到未知的客户端消息类型: {client_message.type}")
                return None
                
        except Exception as e:
            logger.error(f"处理客户端消息时出错: {e}")
            await self._send_error_message(websocket, None, str(e))
            return None
    
    def register_remote_workflow(self, script_name: str, description: str = "") -> None:
        """注册一个远程工作流脚本"""
        self._known_remote_workflows[script_name] = description
        logger.info(f"注册远程工作流脚本: {script_name}")
    
    def is_known_remote_workflow(self, script_name: str) -> bool:
        """检查是否为已知的远程工作流脚本 - 现在自动接受所有脚本名称"""
        # 自动注册未知的工作流脚本
        if script_name not in self._known_remote_workflows:
            self.register_remote_workflow(script_name, f"动态注册的远程工作流: {script_name}")
        return True  # 总是返回 True，因为我们自动注册
    
    async def _handle_start_workflow_instance(self, websocket: WebSocket, message: WorkflowClientMessage) -> Optional[str]:
        """处理启动工作流实例请求，返回实例ID"""
        script_name = message.workflow_script_name
        if not script_name:
            await self._send_error_message(
                websocket, 
                message.client_request_id,
                "缺少工作流脚本名称"
            )
            return None
        
        # 自动注册工作流脚本（如果不存在）
        if not self.is_known_remote_workflow(script_name):
            logger.info(f"自动注册远程工作流脚本: {script_name}")
        
        # 生成实例 ID
        instance_id = f"remote_{uuid.uuid4().hex[:12]}"
        
        # 注册连接和队列a
        self._connections[instance_id] = websocket
        self._step_request_queues[instance_id] = asyncio.Queue()
        self._result_send_queues[instance_id] = asyncio.Queue()
        
        # 发送成功响应
        response = WorkflowServerMessage(
            type=MessageType.WORKFLOW_INSTANCE_STARTED,
            workflow_instance_id=instance_id,
            client_request_id=message.client_request_id
        )
        await websocket.send_text(response.model_dump_json())
        logger.info(f"工作流实例已启动: {instance_id} (脚本: {script_name})")
        
        return instance_id
    
    async def _handle_define_step(self, websocket: WebSocket, message: WorkflowClientMessage) -> None:
        """处理步骤定义消息"""
        instance_id = message.workflow_instance_id
        if not instance_id or instance_id not in self._step_request_queues:
            await self._send_error_message(
                websocket,
                message.client_request_id,
                f"无效的工作流实例ID: {instance_id}"
            )
            return
        
        # 将步骤定义放入队列，供代理生成器获取
        step_request = {
            'client_request_id': message.client_request_id,
            'step_definition': message.step_definition
        }
        await self._step_request_queues[instance_id].put(step_request)
        logger.debug(f"已将步骤定义加入队列 (实例 {instance_id}): {message.step_definition.operation}")
    
    async def _send_error_message(self, websocket: WebSocket, request_id: Optional[str], error: str) -> None:
        """发送错误消息给客户端"""
        response = WorkflowServerMessage(
            type=MessageType.ERROR,
            client_request_id=request_id,
            error=error
        )
        try:
            await websocket.send_text(response.model_dump_json())
            logger.error(f"发送错误消息给客户端: {error}")
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")
    
    async def get_next_step_definition(self, instance_id: str) -> Dict[str, Any]:
        """供代理生成器调用：获取下一个步骤定义"""
        if instance_id not in self._step_request_queues:
            raise RuntimeError(f"工作流实例不存在: {instance_id}")
        
        
        
        # 等待客户端发送步骤定义
        step_request = await self._step_request_queues[instance_id].get()
        return step_request
    
    async def send_step_result(self, instance_id: str, client_request_id: str, result: Any) -> None:
        """供代理生成器调用：发送步骤执行结果给客户端"""
        if instance_id not in self._connections:
            logger.error(f"尝试发送结果到不存在的实例: {instance_id}")
            return
        
        websocket = self._connections[instance_id]
        response = WorkflowServerMessage(
            type=MessageType.STEP_RESULT,
            workflow_instance_id=instance_id,
            client_request_id=client_request_id,
            result=result
        )
        
        try:
            await websocket.send_text(response.model_dump_json())
            logger.debug(f"已发送步骤结果 (实例 {instance_id}, 请求 {client_request_id})")
        except Exception as e:
            logger.error(f"发送步骤结果失败: {e}")

    async def send_ready_for_next_step(self, instance_id: str) -> None:
        """发送准备好接收下一个步骤的信号给客户端"""
        if instance_id not in self._connections:
            logger.error(f"尝试发送准备好信号到不存在的实例: {instance_id}")
            return
        
        websocket = self._connections[instance_id]
        response = WorkflowServerMessage(
            type=MessageType.READY_FOR_NEXT_STEP,
            workflow_instance_id=instance_id
        )
        
        try:
            await websocket.send_text(response.model_dump_json())
            logger.debug(f"已发送准备好信号 (实例 {instance_id})")
        except Exception as e:
            logger.error(f"发送准备好信号失败: {e}")


# 全局连接管理器实例（向后兼容）
_connection_manager: Optional[RemoteWorkflowConnectionManager] = None

async def get_connection_manager() -> RemoteWorkflowConnectionManager:
    """获取全局连接管理器实例（异步版本，推荐使用）"""
    return await RemoteWorkflowConnectionManager.get_instance()

def get_connection_manager_sync() -> RemoteWorkflowConnectionManager:
    """获取全局连接管理器实例（同步版本，向后兼容）"""
    return RemoteWorkflowConnectionManager.get_instance_sync()


def _convert_remote_step_to_workflow_step(step_def: RemoteStepDefinition) -> WorkflowStep:
    """将远程步骤定义转换为 WorkflowStep 对象"""
    operation = step_def.operation.upper()
    
    if operation == "EXECUTE":
        return ExecuteStep(
            name=step_def.name,
            description=step_def.description or "远程执行步骤",
            actions=step_def.actions or []
        )
    elif operation == "CONDITION":
        return ConditionStep(
            name=step_def.name,
            condition_description=step_def.condition_description or step_def.description or "远程条件步骤"
        )
    elif operation == "GENERATE":
        return GenerateStep(
            name=step_def.name,
            content_description=step_def.content_description or step_def.description or "远程生成步骤",
            wait_user=step_def.wait_user or False,
            return_content=step_def.return_content if step_def.return_content is not None else True
        )
    elif operation == "USER_INPUT":
        return UserInputStep(
            name=step_def.name,
            description=step_def.description or "远程用户输入步骤"
        )
    elif operation == "SWITCH":
        return SwitchStep(
            name=step_def.name,
            description=step_def.description or "远程分支步骤",
            cases=step_def.cases or []
        )
    elif operation == "HALT":
        return HaltStep(
            name=step_def.name,
            description=step_def.description or "远程终止步骤"
        )
    else:
        raise ValueError(f"未知的步骤操作类型: {operation}")


async def create_proxy_generator(
    workflow_instance_id: str, 
    script_name: str, 
    conn_manager: RemoteWorkflowConnectionManager
) -> AsyncGenerator[WorkflowStep, Any]:
    """
    创建代理生成器，桥接远程客户端和本地工作流系统。
    
    该生成器的工作流程：
    1. 发送准备好信号给客户端
    2. 等待客户端发送步骤定义
    3. 将步骤定义转换为 WorkflowStep 对象并 yield
    4. 接收本地系统的执行结果并发送给客户端
    5. 重复上述过程直到工作流结束
    """
    logger.info(f"开始代理生成器 (实例 {workflow_instance_id}, 脚本 {script_name})")
    current_client_request_id: Optional[str] = None
    
    try:
        while True:
            # 首先发送准备好信号给客户端
            logger.debug(f"发送准备好信号 (实例 {workflow_instance_id})")
            await conn_manager.send_ready_for_next_step(workflow_instance_id)
            
            # 等待客户端发送下一个步骤定义
            logger.debug(f"等待步骤定义 (实例 {workflow_instance_id})")
            step_request = await conn_manager.get_next_step_definition(workflow_instance_id)
            
            current_client_request_id = step_request['client_request_id']
            step_definition = step_request['step_definition']
            
            # 转换为 WorkflowStep 对象
            try:
                workflow_step = _convert_remote_step_to_workflow_step(step_definition)
                logger.debug(f"转换步骤定义成功 (实例 {workflow_instance_id}): {workflow_step.__class__.__name__}")
            except Exception as e:
                logger.error(f"转换步骤定义失败: {e}")
                await conn_manager.send_step_result(
                    workflow_instance_id,
                    current_client_request_id,
                    f"步骤定义错误: {str(e)}"
                )
                continue
            
            # 检查是否为 HALT 步骤
            if isinstance(workflow_step, HaltStep):
                logger.info(f"代理生成器收到 HALT 步骤，工作流结束 (实例 {workflow_instance_id})")
                # 发送确认消息给客户端
                await conn_manager.send_step_result(
                    workflow_instance_id,
                    current_client_request_id,
                    "工作流已终止"
                )
                return  # 结束生成器
            
            # Yield 步骤给本地工作流系统
            result_from_agent = yield workflow_step
            
            # 将结果发送回客户端
            await conn_manager.send_step_result(
                workflow_instance_id,
                current_client_request_id,
                result_from_agent
            )
            
    except Exception as e:
        logger.error(f"代理生成器出错 (实例 {workflow_instance_id}): {e}")
        if current_client_request_id:
            await conn_manager.send_step_result(
                workflow_instance_id,
                current_client_request_id,
                f"服务器内部错误: {str(e)}"
            )
        raise
    finally:
        logger.info(f"代理生成器结束 (实例 {workflow_instance_id})")


def create_remote_workflow_for_agent(script_name: str, workflow_instance_id: str) -> 'WorkflowSource':
    """为 Agent 创建远程工作流源，专门用于 client_api.py"""
    
    # 获取连接管理器实例
    conn_manager = RemoteWorkflowConnectionManager.get_instance_sync()
    
    def generator_function_factory(instance_id: str):
        def create_generator():
            return create_proxy_generator(workflow_instance_id, script_name, conn_manager)
        return create_generator
    
    remote_source = RemoteWorkflowSource(
        remote_workflow_script_name=script_name,
        description=f"动态远程工作流: {script_name}",
        initial_registers={},
        generator_function_factory=generator_function_factory
    )
    
    return WorkflowSource(
        type='remote',
        source=remote_source
    ) 