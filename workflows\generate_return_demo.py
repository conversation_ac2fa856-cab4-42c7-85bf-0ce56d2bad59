from typing import Dict, Any, AsyncGenerator
from libraries.workflow_context import WorkflowContext
from libraries.workflow_models import UserInputStep, WorkflowStep, NopStep, ExecuteStep, ConditionStep, ActionDefinition, GenerateStep
from message import UserMessage

# --- 工作流元数据 ---
WORKFLOW_DESCRIPTION = "演示GenerateStep返回生成内容的功能"
INITIAL_REGISTERS: Dict[str, Any] = {}

# --- 工作流生成器函数 ---
async def steps(ctx: WorkflowContext) -> AsyncGenerator[WorkflowStep, Any]:
    """
    演示GenerateStep返回生成内容功能的工作流。
    """
    
    # 等待用户输入
    user_message: UserMessage = yield UserInputStep(
        name="get_user_input",
        description="等待用户输入他们想要生成的内容主题"
    )
    
    # 第一次生成：创建一个故事大纲
    story_outline = await ctx.generate(
        name="generate_story_outline",
        description="根据用户的主题生成一个简短的故事大纲",
        return_content=True  # 返回生成的内容
    )
    
    # 将生成的大纲存储到寄存器中
    ctx.set_register("story_outline", story_outline)

    # 显示生成的大纲给用户确认
    await ctx.generate(
        name="show_outline_to_user",
        description=f"向用户展示生成的故事大纲并询问是否满意：\n{story_outline}",
        return_content=False  # 不返回内容，直接作为回复
    )

    # 等待用户确认
    user_feedback: UserMessage = yield UserInputStep(
        name="get_user_feedback",
        description="等待用户对故事大纲的反馈"
    )
    
    # 第二次生成：基于大纲创建详细故事
    detailed_story = await ctx.generate(
        name="generate_detailed_story",
        description=f"基于以下大纲创建一个详细的故事：\n{story_outline}",
        return_content=True
    )
    
    # 将详细故事也存储到寄存器中
    ctx.set_register("detailed_story", detailed_story)
    
    # 第三次生成：创建故事总结并直接回复给用户
    await ctx.generate(
        name="generate_final_response",
        description=f"为用户总结整个创作过程，包括：\n1. 原始主题\n2. 故事大纲：{story_outline}\n3. 详细故事：{detailed_story}\n请用友好的语调回复用户",
        return_content=False  # 不返回内容，直接作为最终回复
    )
    
    # 结束工作流
    await ctx.halt()
